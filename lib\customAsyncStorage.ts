import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Create a custom storage adapter that works for both web and native
const createCustomStorage = () => {
  // Check if we're in a browser environment with localStorage
  const hasLocalStorage = () => {
    try {
      return (
        Platform.OS === "web" &&
        typeof window !== "undefined" &&
        window.localStorage !== undefined
      );
    } catch (e) {
      return false;
    }
  };

  return {
    async getItem(key: string) {
      try {
        if (hasLocalStorage()) {
          // Use localStorage for web
          return window.localStorage.getItem(key);
        }
        return await AsyncStorage.getItem(key);
      } catch (error) {
        console.error("Error getting item from storage:", error);
        return null;
      }
    },
    async setItem(key: string, value: string) {
      try {
        if (hasLocalStorage()) {
          // Use localStorage for web
          window.localStorage.setItem(key, value);
          return;
        }
        return await AsyncStorage.setItem(key, value);
      } catch (error) {
        console.error("Error setting item in storage:", error);
      }
    },
    async removeItem(key: string) {
      try {
        if (hasLocalStorage()) {
          // Use localStorage for web
          window.localStorage.removeItem(key);
          return;
        }
        return await AsyncStorage.removeItem(key);
      } catch (error) {
        console.error("Error removing item from storage:", error);
      }
    },
  };
};

export default createCustomStorage;
