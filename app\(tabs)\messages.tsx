import React from "react";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import { H1 } from "~/components/ui/typography";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { MessageSquare, Clock } from "lucide-react-native";

export default function MessagesScreen() {
  return (
    <View className="flex-1 p-4 bg-background">
      <View className="mb-6">
        <H1 className="text-2xl font-bold text-foreground mb-2">Messages</H1>
        <Text className="text-muted-foreground">
          Discussions liées à vos événements
        </Text>
      </View>

      {/* État vide pour le moment */}
      <View className="flex-1 justify-center items-center gap-4">
        <View className="bg-muted/20 p-6 rounded-full">
          <MessageSquare size={48} className="text-muted-foreground" />
        </View>
        <View className="items-center gap-2">
          <Text className="text-xl font-semibold text-foreground">
            Aucune discussion
          </Text>
          <Text className="text-muted-foreground text-center max-w-sm">
            Les discussions de groupe pour vos événements apparaîtront ici.
          </Text>
        </View>
        
        <Card className="mt-4 border border-border bg-muted/10">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex-row items-center gap-2">
              <Clock size={20} className="text-primary" />
              Bientôt disponible
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-sm text-muted-foreground">
              La messagerie sera disponible dans une prochaine mise à jour pour faciliter 
              la communication entre les participants de vos événements.
            </Text>
          </CardContent>
        </Card>
      </View>
    </View>
  );
}
