import React, { createContext, useContext, useEffect, useState } from "react";
import { Session } from "@supabase/supabase-js";
import { supabase } from "./supabase";
import { createProfile, fetchProfile } from "./supabaseCrud";
import { checkUserProfile, createUserProfileIfMissing } from "./checkDatabase";
import { createProfileForUser } from "./fixDatabase";

interface AuthContextType {
  session: Session | null;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    const fetchSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setSession(session);
    };

    fetchSession();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);

      // Créer automatiquement un profil lors de l'inscription ou connexion
      if ((event === "SIGNED_UP" || event === "SIGNED_IN") && session?.user) {
        try {
          console.log("Vérification/création du profil utilisateur...");

          // Essayer de créer le profil directement avec la méthode robuste
          const userName =
            session.user.user_metadata?.name ||
            session.user.email?.split("@")[0] ||
            "Utilisateur";

          // Utiliser la méthode de création forcée qui gère mieux les erreurs
          const profile = await createProfileForUser(session.user.id, userName);

          if (profile) {
            console.log("Profil créé/vérifié avec succès pour l'utilisateur");
          } else {
            console.warn(
              "Impossible de créer le profil, mais l'app continuera"
            );
          }
        } catch (error) {
          console.error(
            "Erreur lors de la création automatique du profil:",
            error
          );
          // Ne pas bloquer l'authentification même si la création de profil échoue
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
  };

  return (
    <AuthContext.Provider value={{ session, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
