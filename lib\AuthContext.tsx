import React, { createContext, useContext, useEffect, useState } from "react";
import { Session } from "@supabase/supabase-js";
import { supabase } from "./supabase";
import { createProfile, fetchProfile } from "./supabaseCrud";

interface AuthContextType {
  session: Session | null;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    const fetchSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setSession(session);
    };

    fetchSession();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);

      // Créer automatiquement un profil lors de l'inscription ou connexion
      if ((event === "SIGNED_UP" || event === "SIGNED_IN") && session?.user) {
        try {
          console.log("Vérification/création du profil utilisateur...");

          // Vérifier si le profil existe déjà
          const existingProfile = await fetchProfile(session.user.id);

          if (!existingProfile) {
            // Créer un nouveau profil avec les données de base
            const userName =
              session.user.user_metadata?.name ||
              session.user.email?.split("@")[0] ||
              "Utilisateur";

            const profile = await createProfile(session.user.id, {
              name: userName,
              avatar_url: session.user.user_metadata?.avatar_url || null,
            });

            if (profile) {
              console.log("Profil créé avec succès pour l'utilisateur");
            } else {
              console.warn(
                "Impossible de créer le profil, mais l'app continuera"
              );
            }
          }
        } catch (error) {
          console.error(
            "Erreur lors de la création automatique du profil:",
            error
          );
          // Ne pas bloquer l'authentification même si la création de profil échoue
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
  };

  return (
    <AuthContext.Provider value={{ session, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
