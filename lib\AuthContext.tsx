import React, { createContext, useContext, useEffect, useState } from "react";
import { Session } from "@supabase/supabase-js";
import { supabase } from "./supabase";
import { createProfile, fetchProfile } from "./supabaseCrud";

interface AuthContextType {
  session: Session | null;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);

  useEffect(() => {
    const fetchSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setSession(session);
    };

    fetchSession();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);

      // Créer automatiquement un profil lors de l'inscription
      if (event === "SIGNED_UP" && session?.user) {
        try {
          // Vérifier si le profil existe déjà
          const existingProfile = await fetchProfile(session.user.id);

          if (!existingProfile) {
            // Créer un nouveau profil avec les données de base
            await createProfile(session.user.id, {
              name:
                session.user.user_metadata?.name ||
                session.user.email?.split("@")[0] ||
                "Utilisateur",
              avatar_url: session.user.user_metadata?.avatar_url || null,
            });
            console.log(
              "Profil créé automatiquement pour le nouvel utilisateur"
            );
          }
        } catch (error) {
          console.error(
            "Erreur lors de la création automatique du profil:",
            error
          );
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
  };

  return (
    <AuthContext.Provider value={{ session, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
