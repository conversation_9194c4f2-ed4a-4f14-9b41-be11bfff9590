import { Tabs } from "expo-router";
import React from "react";

export default function TabLayout() {
  // Using expo-router's Tabs, similar to the working TestTabsApp
  // We are not importing screen components here, expo-router handles it by file name.
  return (
    <Tabs>
      <Tabs.Screen
        name="index" // Corresponds to app/(tabs)/index.tsx
        options={{ title: "Events (Test)" }}
      />
      <Tabs.Screen
        name="settings_test" // Corresponds to app/(tabs)/settings_test.tsx
        options={{ title: "Settings (Test)" }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profil",
          // tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
        }}
      />
      <Tabs.Screen
        name="contacts"
        options={{
          title: "Contacts",
          // tabBarIcon: ({ color, size }) => <Users size={size} color={color} />,
        }}
      />
      {/* Tasks screen remains commented out for now
      <Tabs.Screen
        name="tasks"
        options={{
          title: "Mes Tâches",
          // tabBarIcon: ({ color, size }) => <ListTodo size={size} color={color} />,
        }}
      />
      */}
      {/* Removed duplicate commented out profile screen */}
    </Tabs>
  );
}
