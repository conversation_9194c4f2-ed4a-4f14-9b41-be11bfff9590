"use client";

import * as React from "react";
import { createPortal } from "react-dom";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export function Modal({ isOpen, onClose, title, children }: ModalProps) {
  if (!isOpen) return null;

  // Utiliser createPortal pour rendre le modal au niveau racine du DOM
  // Cela évite les problèmes de z-index et de positionnement
  const modalContent = (
    <div
      className="fixed inset-0 z-[999999] flex items-center justify-center bg-black/50"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 999999, // Z-index très élevé pour s'assurer qu'il est au-dessus de tout
      }}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-xl overflow-hidden"
        style={{
          position: "relative",
          maxWidth: "95vw",
          width: "auto",
          maxHeight: "90vh",
          zIndex: 1000000, // Z-index encore plus élevé
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 border-b">
          <span className="text-lg font-semibold">{title}</span>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full"
            style={{ fontSize: "24px", lineHeight: "24px" }}
          >
            <span className="text-gray-500">×</span>
          </button>
        </div>
        <div
          className="p-4 overflow-auto"
          style={{ maxHeight: "calc(90vh - 60px)" }}
        >
          {children}
        </div>
      </div>
    </div>
  );

  // Utiliser createPortal pour rendre le modal au niveau racine du DOM
  // Cela évite les problèmes de z-index et de positionnement
  return createPortal(modalContent, document.body);
}
