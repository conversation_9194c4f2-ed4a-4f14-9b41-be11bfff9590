// --- Begin app/event/[id]/index.tsx ---
import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform,
} from "react-native";
import { useLocalSearchParams, useRouter, useNavigation } from "expo-router";
import { Text } from "~/components/ui/text";
import { P } from "~/components/ui/typography";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  fetchEventDetails,
  updateEvent,
  fetchParticipantsForEvent,
  deleteEvent,
} from "~/lib/supabaseCrud";
import { Separator } from "~/components/ui/separator";
import { CustomButton as Button } from "~/components/ui/custom-button"; // Changed to CustomButton
import { Switch } from "~/components/ui/switch";
import { Label } from "~/components/ui/label";
import {
  CalendarDays,
  Clock,
  MapPin,
  Users,
  MessageSquare,
  <PERSON>Tod<PERSON>,
  Trash2,
} from "lucide-react-native";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { EventItemsSection } from "~/components/EventItemsSection"; // Added import

export default function EventDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();
  const navigation = useNavigation();
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Fonction pour charger les détails de l'événement
  const loadEventDetails = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);

      // Charger l'événement et les participants en parallèle
      const [eventDetails, eventParticipants] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
      ]);

      if (eventDetails) {
        setEvent(eventDetails);
        // Mettre à jour le titre de la page avec le nom de l'événement
        navigation.setOptions({
          title: eventDetails.title || "Détails Événement",
        });
      }

      setParticipants(eventParticipants || []);
    } catch (error) {
      console.error("Error loading event details:", error);
      showToast("Erreur lors du chargement des détails.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  // Charger les données une seule fois au montage du composant
  useEffect(() => {
    loadEventDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdate = async (field: string, value: any) => {
    if (!event || !session || session.user.id !== event.organizer_id) {
      showToast("Vous n'avez pas les droits pour modifier cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      setUpdateLoading(true);
      const updatedEvent = await updateEvent(event.id, { [field]: value });

      if (updatedEvent) {
        showToast("Option mise à jour !", { type: "success" });
        setEvent(updatedEvent);
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error updating event:", error);
      showToast("Erreur lors de la mise à jour.", { type: "error" });
    } finally {
      setUpdateLoading(false);
    }
  };

  // Fonction pour supprimer l'événement
  const handleDeleteEvent = async () => {
    console.log("handleDeleteEvent appelé");

    if (!event || !session || session.user.id !== event.organizer_id) {
      console.log("Vérification des droits échouée:", {
        event: !!event,
        session: !!session,
        isOrganizer: session?.user.id === event?.organizer_id,
      });
      showToast("Vous n'avez pas les droits pour supprimer cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      console.log("Début de la suppression de l'événement", event.id);
      setDeleteLoading(true);
      const success = await deleteEvent(event.id);
      console.log("Résultat de la suppression:", success);

      if (success) {
        showToast("Événement supprimé avec succès !", { type: "success" });
        // Retourner à la page précédente
        console.log("Navigation en arrière après suppression");
        router.back();
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      showToast("Erreur lors de la suppression.", { type: "error" });
    } finally {
      setDeleteLoading(false);
      console.log("Fin de handleDeleteEvent");
    }
  };

  // Si l'événement est en cours de chargement, afficher un loader
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" className="text-primary" />
      </View>
    );
  }

  // Si l'événement n'existe pas, afficher un message d'erreur
  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;
  const eventDate = new Date(event.date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="py-6 px-4 max-w-3xl mx-auto">
        <Card className="mb-4 border border-border bg-white rounded-xl shadow-sm">
          <CardHeader className="items-center pb-2">
            <Text className="text-6xl mb-2">{event.icon || "🎉"}</Text>
            <CardTitle className="text-2xl text-center font-bold text-foreground">
              {event.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            {event.description && (
              <>
                <P className="text-center text-muted-foreground">
                  {event.description}
                </P>
                <Separator />
              </>
            )}
            <View className="flex-row items-center">
              <CalendarDays size={18} className="text-muted-foreground mr-3" />
              <Text className="text-base text-foreground">{formattedDate}</Text>
            </View>
            <View className="flex-row items-center">
              <Clock size={18} className="text-muted-foreground mr-3" />
              <Text className="text-base text-foreground">{formattedTime}</Text>
            </View>
            {event.location && (
              <View className="flex-row items-center">
                <MapPin size={18} className="text-muted-foreground mr-3" />
                <Text className="text-base text-foreground">
                  {event.location}
                </Text>
              </View>
            )}
          </CardContent>
        </Card>

        <Card className="mb-4 border border-border bg-white rounded-xl shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Actions</CardTitle>
          </CardHeader>
          <CardContent className="gap-4">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <Users size={18} className="text-muted-foreground mr-3" />
                <Text>Voir les participants ({participants.length})</Text>
              </View>
              <Button
                variant="outline"
                size="sm"
                onPress={() =>
                  Alert.alert(
                    "Participants",
                    "Cette fonctionnalité sera disponible prochainement."
                  )
                }
              >
                <Text>Voir</Text>
              </Button>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <ListTodo size={18} className="text-muted-foreground mr-3" />
                <Text>Voir les items à apporter</Text>
              </View>
              <Button
                variant="outline"
                size="sm"
                onPress={() =>
                  Alert.alert(
                    "Items",
                    "Cette fonctionnalité sera disponible prochainement."
                  )
                }
              >
                <Text>Voir</Text>
              </Button>
            </View>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <MessageSquare
                  size={18}
                  className="text-muted-foreground mr-3"
                />
                <Text>Accéder à la discussion</Text>
              </View>
              <Button
                variant="outline"
                size="sm"
                disabled={true}
                onPress={() => {}}
              >
                <Text>Bientôt disponible</Text>
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Item Section Card */}
        {event && id && (
          <EventItemsSection
            eventId={parseInt(id as string, 10)}
            isOrganizer={isOrganizer}
            allowPreAssignment={event.allow_pre_assignment} // Pass event option
          />
        )}

        {isOrganizer && (
          <Card className="mb-6 border border-border bg-white rounded-xl shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Options de l'Événement
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-4">
              <View className="flex-row items-center justify-between">
                <Label
                  nativeID="suggestionsOptLabel"
                  className="flex-1 mr-2 text-base"
                >
                  Autoriser les suggestions d'items
                </Label>
                {updateLoading ? (
                  <ActivityIndicator size="small" className="text-primary" />
                ) : (
                  <Switch
                    nativeID="suggestionsOptLabel"
                    checked={event.allow_suggestions}
                    onCheckedChange={(value) =>
                      handleUpdate("allow_suggestions", value)
                    }
                  />
                )}
              </View>
              <View className="flex-row items-center justify-between">
                <Label
                  nativeID="preAssignOptLabel"
                  className="flex-1 mr-2 text-base"
                >
                  Autoriser la pré-attribution ("Fixer")
                </Label>
                {updateLoading ? (
                  <ActivityIndicator size="small" className="text-primary" />
                ) : (
                  <Switch
                    nativeID="preAssignOptLabel"
                    checked={event.allow_pre_assignment}
                    onCheckedChange={(value) =>
                      handleUpdate("allow_pre_assignment", value)
                    }
                  />
                )}
              </View>
            </CardContent>
          </Card>
        )}

        {/* Bouton de suppression */}
        {isOrganizer && (
          <Button
            variant="destructive"
            className="w-full h-12 mt-4"
            {...(Platform.OS === "web"
              ? {
                  onClick: () => {
                    console.log("Bouton de suppression cliqué (web)");
                    if (
                      confirm(
                        `Cette action est irréversible. Cela supprimera définitivement l'événement "${event?.title}" et toutes les données associées.`
                      )
                    ) {
                      handleDeleteEvent();
                    }
                  },
                }
              : {
                  onPress: () => {
                    console.log("Bouton de suppression cliqué (mobile)");
                    Alert.alert(
                      "Supprimer l'événement",
                      `Cette action est irréversible. Cela supprimera définitivement l'événement "${event?.title}" et toutes les données associées.`,
                      [
                        {
                          text: "Annuler",
                          style: "cancel",
                        },
                        {
                          text: deleteLoading ? "Suppression..." : "Supprimer",
                          style: "destructive",
                          onPress: handleDeleteEvent,
                        },
                      ]
                    );
                  },
                })}
          >
            <Trash2 size={18} className="mr-2" />
            <Text className="text-white font-medium">
              Supprimer l'événement
            </Text>
          </Button>
        )}

        <View className="h-16" />
      </View>
    </ScrollView>
  );
}
// --- End app/event/[id]/index.tsx ---
