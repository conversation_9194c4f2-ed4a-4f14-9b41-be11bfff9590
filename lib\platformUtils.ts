import { Platform } from "react-native";

/**
 * Utilitaire pour gérer les événements de clic/pression en fonction de la plateforme
 * Sur le web, utilise onClick, sur mobile utilise onPress
 * 
 * @param callback Fonction à exécuter lors du clic/pression
 * @returns Un objet avec la propriété onClick ou onPress selon la plateforme
 * 
 * @example
 * <Button {...handlePressEvent(() => doSomething())}>
 *   <Text>Click me</Text>
 * </Button>
 */
export const handlePressEvent = (callback: () => void) => {
  return Platform.OS === "web"
    ? { onClick: callback }
    : { onPress: callback };
};
