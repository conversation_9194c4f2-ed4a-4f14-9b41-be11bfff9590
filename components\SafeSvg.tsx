import React from 'react';
import { Platform } from 'react-native';
import * as RNSVG from 'react-native-svg';

// Create safe versions of SVG components that work in both web and native environments
const createSafeSvgComponent = (Component: any) => {
  return React.forwardRef((props: any, ref: any) => {
    // Filter out touchable properties on web to avoid the error
    if (Platform.OS === 'web') {
      const safeProps = { ...props };
      // Remove touchable properties that cause issues on web
      delete safeProps.onPress;
      delete safeProps.onPressIn;
      delete safeProps.onPressOut;
      delete safeProps.onLongPress;
      
      return <Component {...safeProps} ref={ref} />;
    }
    
    // Use the original component with all props on native
    return <Component {...props} ref={ref} />;
  });
};

// Export safe versions of all SVG components
export const Svg = createSafeSvgComponent(RNSVG.Svg);
export const Circle = createSafeSvgComponent(RNSVG.Circle);
export const Ellipse = createSafeSvgComponent(RNSVG.Ellipse);
export const G = createSafeSvgComponent(RNSVG.G);
export const Text = createSafeSvgComponent(RNSVG.Text);
export const TSpan = createSafeSvgComponent(RNSVG.TSpan);
export const TextPath = createSafeSvgComponent(RNSVG.TextPath);
export const Path = createSafeSvgComponent(RNSVG.Path);
export const Polygon = createSafeSvgComponent(RNSVG.Polygon);
export const Polyline = createSafeSvgComponent(RNSVG.Polyline);
export const Line = createSafeSvgComponent(RNSVG.Line);
export const Rect = createSafeSvgComponent(RNSVG.Rect);
export const Use = createSafeSvgComponent(RNSVG.Use);
export const Image = createSafeSvgComponent(RNSVG.Image);
export const Symbol = createSafeSvgComponent(RNSVG.Symbol);
export const Defs = createSafeSvgComponent(RNSVG.Defs);
export const LinearGradient = createSafeSvgComponent(RNSVG.LinearGradient);
export const RadialGradient = createSafeSvgComponent(RNSVG.RadialGradient);
export const Stop = createSafeSvgComponent(RNSVG.Stop);
export const ClipPath = createSafeSvgComponent(RNSVG.ClipPath);
export const Pattern = createSafeSvgComponent(RNSVG.Pattern);
export const Mask = createSafeSvgComponent(RNSVG.Mask);

// Re-export other utilities from react-native-svg
export const { SvgUri, SvgXml, SvgCss, SvgFromUri, SvgFromXml, SvgCssUri, SvgWithCss, SvgWithCssUri } = RNSVG;
