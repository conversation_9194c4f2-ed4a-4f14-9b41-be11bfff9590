// lib/directApi.ts
// Ce fichier contient des fonctions qui utilisent directement l'API REST de Supabase
// pour contourner les problèmes de politiques de sécurité

import {
  Event,
  Participant,
  ParticipantInsert,
  ParticipantRole,
  ParticipantStatus,
  Profile,
} from "./types";

// URL et clé Supabase depuis les variables d'environnement
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || "";
const SUPABASE_SERVICE_KEY = process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY || "";

// Headers pour les requêtes
const headers = {
  "Content-Type": "application/json",
  apikey: SUPABASE_SERVICE_KEY,
  Authorization: `Bearer ${SUPABASE_SERVICE_KEY}`,
  Prefer: "return=representation",
};

/**
 * Vérifie si un profil existe et le crée s'il n'existe pas
 */
export async function ensureProfileExists(
  userId: string,
  name?: string
): Promise<Profile | null> {
  try {
    // Vérifier si le profil existe déjà
    const checkResponse = await fetch(
      `${SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}`,
      {
        method: "GET",
        headers,
      }
    );

    if (!checkResponse.ok) {
      console.error(
        "Erreur lors de la vérification du profil:",
        checkResponse.statusText
      );
      return null;
    }

    const profiles = await checkResponse.json();

    // Si le profil existe, le retourner
    if (profiles && profiles.length > 0) {
      console.log("Profil existant trouvé:", profiles[0]);
      return profiles[0];
    }

    // Si le profil n'existe pas, le créer
    console.log("Création d'un nouveau profil pour l'utilisateur:", userId);

    const createResponse = await fetch(`${SUPABASE_URL}/rest/v1/profiles`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        id: userId,
        name: name || "Utilisateur",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }),
    });

    if (!createResponse.ok) {
      console.error(
        "Erreur lors de la création du profil:",
        createResponse.statusText
      );
      const errorData = await createResponse.json();
      console.error("Détails de l'erreur:", errorData);
      return null;
    }

    const newProfile = await createResponse.json();
    console.log("Nouveau profil créé:", newProfile);
    return newProfile[0] || null;
  } catch (error) {
    console.error("Exception dans ensureProfileExists:", error);
    return null;
  }
}

/**
 * Crée un événement en utilisant directement l'API REST de Supabase
 */
export async function createEventDirect(
  eventData: Omit<Event, "id" | "created_at" | "updated_at" | "organizer_id">,
  organizerId: string
): Promise<Event | null> {
  console.log("Creating event with direct API:", {
    ...eventData,
    organizer_id: organizerId,
  });

  // Validation des données
  if (!eventData.title) {
    console.error("Event title is required");
    return null;
  }

  if (!eventData.date_time) {
    console.error("Event date_time is required");
    return null;
  }

  // Vérifier que la date est dans le futur
  const eventDate = new Date(eventData.date_time);
  const now = new Date();

  // Réinitialiser les secondes et millisecondes pour une comparaison plus juste
  now.setSeconds(0, 0);
  eventDate.setSeconds(0, 0);

  if (eventDate <= now) {
    console.error("Event date must be in the future");
    throw new Error("La date de l'événement doit être dans le futur");
  }

  try {
    // Vérifier et créer le profil si nécessaire
    console.log("Vérification du profil de l'organisateur:", organizerId);
    const profile = await ensureProfileExists(organizerId);

    if (!profile) {
      console.error(
        "Impossible de créer ou de trouver le profil de l'organisateur"
      );
      throw new Error(
        "Impossible de créer ou de trouver le profil de l'organisateur"
      );
    }

    console.log("Profil de l'organisateur confirmé:", profile);

    // Préparation des données avec des valeurs par défaut si nécessaire
    const eventPayload = {
      ...eventData,
      organizer_id: organizerId,
      // S'assurer que les booléens ont des valeurs par défaut
      allow_suggestions: eventData.allow_suggestions ?? false,
      allow_pre_assignment: eventData.allow_pre_assignment ?? false,
      organizer_delegated: eventData.organizer_delegated ?? false,
    };

    console.log("Sending event payload to Supabase direct API:", eventPayload);

    // Utiliser fetch pour appeler directement l'API REST de Supabase
    const response = await fetch(`${SUPABASE_URL}/rest/v1/events`, {
      method: "POST",
      headers,
      body: JSON.stringify([eventPayload]),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error creating event:", response.status, errorText);
      throw new Error(
        `Erreur lors de la création de l'événement: ${errorText}`
      );
    }

    const data = await response.json();
    console.log("Event created successfully with direct API:", data);

    // L'API retourne un tableau, nous prenons le premier élément
    return data[0] as Event;
  } catch (e) {
    console.error("Exception in createEventDirect:", e);
    // Propager l'erreur pour une meilleure gestion côté client
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error("Erreur lors de la création de l'événement");
    }
  }
}

/**
 * Crée un participant en utilisant directement l'API REST de Supabase
 */
export async function createParticipantDirect(
  participantData: ParticipantInsert
): Promise<Participant | null> {
  console.log("Creating participant with direct API:", participantData);

  // Validation des données
  if (!participantData.event_id) {
    console.error("Participant event_id is required");
    return null;
  }

  // Vérifier si c'est un utilisateur enregistré ou anonyme
  const insertData: Partial<ParticipantInsert> = { ...participantData };
  if (insertData.user_id) {
    // Utilisateur enregistré - effacer les champs anonymes
    insertData.anonymous_name = null;
    insertData.anonymous_email = null;
    insertData.anonymous_phone = null;
  } else if (!insertData.anonymous_name) {
    console.error(
      "Cannot create participant without user_id or anonymous_name"
    );
    return null;
  }

  // S'assurer que les valeurs par défaut sont définies
  insertData.role = insertData.role || ParticipantRole.Guest;
  insertData.status = insertData.status || ParticipantStatus.Pending;

  try {
    // Si c'est un utilisateur enregistré, vérifier et créer son profil si nécessaire
    if (insertData.user_id) {
      console.log("Vérification du profil du participant:", insertData.user_id);
      const profile = await ensureProfileExists(insertData.user_id);

      if (!profile) {
        console.error(
          "Impossible de créer ou de trouver le profil du participant"
        );
        throw new Error(
          "Impossible de créer ou de trouver le profil du participant"
        );
      }

      console.log("Profil du participant confirmé:", profile);
    }

    console.log(
      "Sending participant payload to Supabase direct API:",
      insertData
    );

    // Utiliser fetch pour appeler directement l'API REST de Supabase
    const response = await fetch(`${SUPABASE_URL}/rest/v1/participants`, {
      method: "POST",
      headers,
      body: JSON.stringify([insertData]),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error creating participant:", response.status, errorText);
      throw new Error(
        `Erreur lors de la création du participant: ${errorText}`
      );
    }

    const data = await response.json();
    console.log("Participant created successfully with direct API:", data);

    // L'API retourne un tableau, nous prenons le premier élément
    return data[0] as Participant;
  } catch (e) {
    console.error("Exception in createParticipantDirect:", e);
    // Propager l'erreur pour une meilleure gestion côté client
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error("Erreur lors de la création du participant");
    }
  }
}
