// Web-specific Supabase client
import { createClient } from "@supabase/supabase-js";
import { Database } from "./database.types"; // Import generated types

// Charger les variables d'environnement via le mécanisme intégré d'Expo
const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL ||
  "https://your-supabase-url.supabase.co";
const supabaseAnonKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "your-anon-key";
const supabaseServiceKey =
  process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY || "your-service-key";

// Vérification que les clés sont bien chargées
if (supabaseUrl === "https://your-supabase-url.supabase.co") {
  console.warn(
    "Using default Supabase URL. Set EXPO_PUBLIC_SUPABASE_URL in your .env file for production."
  );
}
if (supabaseAnonKey === "your-anon-key") {
  console.warn(
    "Using default Supabase Anon Key. Set EXPO_PUBLIC_SUPABASE_ANON_KEY in your .env file for production."
  );
}

// Create Supabase client for web
export const supabaseWeb = createClient<Database>(
  supabaseUrl,
  supabaseAnonKey,
  {
    // Add Database type generic
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);

// Create Supabase admin client for web with service role key
export const supabaseAdminWeb = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey,
  {
    // Add Database type generic
    auth: {
      persistSession: false,
    },
  }
);
