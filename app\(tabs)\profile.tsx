import React, { useState, useEffect, useCallback } from "react"; // Added useCallback
import {
  View,
  ScrollView,
  ActivityIndicator,
  Pressable,
  Platform,
  StyleSheet,
  Alert, // Added for handlers
} from "react-native";

// Context and Auth
import { useAuth } from "~/lib/AuthContext";
// import { supabase } from "~/lib/supabase"; // Keep commented
import CreateAccountForm from "~/components/CreateAccountForm";
import SignInForm from "~/components/SignInForm";

// UI Components
import { Text } from "~/components/ui/text";
import { H1, H3, Muted } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
} from "~/components/ui/card";
import { Switch } from "~/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";

// Lucide Icons
import {
  LogOut,
  Save,
  UserCircle,
  Pencil,
  BellRing,
  ShieldCheck,
  AlertTriangle,
  LogIn,
  UserPlus,
  Edit3,
} from "lucide-react-native";

// Supabase and App Logic
import { fetchProfile, updateProfile } from "~/lib/supabaseCrud";
import { Profile } from "~/lib/types"; // Ensure Profile type includes is_discoverable
import { showToast } from "~/lib/toastService";
import { DatabaseDiagnostic } from "~/components/DatabaseDiagnostic";

// Enum for auth view state
enum AuthView {
  NONE,
  CREATE_ACCOUNT,
  SIGN_IN,
}

export default function ProfileScreen() {
  const { session, signOut: contextSignOut } = useAuth();
  const authUser = session?.user;
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [name, setName] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isDiscoverable, setIsDiscoverable] = useState(false);
  const [currentAuthView, setCurrentAuthView] = useState<AuthView>(
    AuthView.NONE
  );

  // Load profile if session exists - RESTORED
  useEffect(() => {
    const loadProfileData = async () => {
      if (authUser?.id) {
        setLoading(true);
        try {
          const fetchedProfile = await fetchProfile(authUser.id);
          if (fetchedProfile) {
            setProfile(fetchedProfile);
            setName(fetchedProfile.name || "");
            setAvatarUrl(fetchedProfile.avatar_url || "");
            // Ensure is_discoverable is handled, assuming it's in fetchedProfile type
            setIsDiscoverable(fetchedProfile.is_discoverable || false);
          }
        } catch (error: any) {
          showToast(error.message || "Impossible de charger le profil.", {
            type: "error",
          });
        } finally {
          setLoading(false);
        }
      } else {
        setProfile(null);
        setCurrentAuthView(AuthView.NONE);
      }
    };
    loadProfileData();
  }, [authUser]); // Removed loadProfileData from dep array as it's defined inside

  // Platform-specific press handling - RESTORED
  const handlePress = (callback: () => void) => {
    return Platform.OS === "web"
      ? { onClick: callback }
      : { onPress: callback };
  };

  // handleUpdateProfile - RESTORED
  const handleUpdateProfile = async () => {
    if (!authUser?.id) return;
    setLoading(true);
    const updates: Partial<Profile> = {
      // Use Partial<Profile> for updates
      // id: authUser.id, // id is usually not part of update payload for RLS, PK is used in query
      name: name,
      avatar_url: avatarUrl,
      is_discoverable: isDiscoverable,
    };
    try {
      const updatedProfileData = await updateProfile(authUser.id, updates);
      if (updatedProfileData) {
        setProfile(updatedProfileData);
        showToast("Profil mis à jour !", { type: "success" });
        setIsEditing(false);
      } else {
        throw new Error("La mise à jour du profil a échoué.");
      }
    } catch (error: any) {
      showToast(error.message || "Impossible de mettre à jour le profil.", {
        type: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // handleSignOut - RESTORED
  const handleSignOut = async () => {
    setAuthLoading(true);
    await contextSignOut();
    setAuthLoading(false);
    setProfile(null);
    setCurrentAuthView(AuthView.NONE);
    setIsEditing(false);
    showToast("Déconnexion réussie.", { type: "info" });
  };

  // handleDeleteAccount - RESTORED
  const handleDeleteAccount = () => {
    showToast("La suppression de compte sera bientôt disponible.", {
      type: "info",
    });
  };

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={styles.text}>Profil Page (Effect & Handlers Restored)</Text>
      {/* Display some state to see if effect ran */}
      {loading && <ActivityIndicator />}
      {profile && <Text>Profile loaded: {profile.name}</Text>}
      {!session && <Text>No session</Text>}

      {/* Composant de diagnostic pour déboguer les problèmes de base de données */}
      <DatabaseDiagnostic />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: "#f0f0f0",
  },
  contentContainer: {
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    minHeight: "100%",
  },
  text: {
    fontSize: 20,
    color: "#333",
    marginBottom: 10,
  },
});
