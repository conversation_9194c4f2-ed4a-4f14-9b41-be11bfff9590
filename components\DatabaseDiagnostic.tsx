import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { Button } from '~/components/ui/button';
import { Text } from '~/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { useAuth } from '~/lib/AuthContext';
import { runFullDiagnostic, diagnoseDatabase, testBasicOperations, createProfileForUser } from '~/lib/fixDatabase';
import { fetchProfile } from '~/lib/supabaseCrud';

export function DatabaseDiagnostic() {
  const { session } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const runDiagnostic = async () => {
    if (!session?.user?.id) {
      addLog('❌ Aucun utilisateur connecté');
      return;
    }

    setIsRunning(true);
    addLog('🚀 Démarrage du diagnostic...');

    try {
      // Override console.log temporarily to capture logs
      const originalLog = console.log;
      const originalWarn = console.warn;
      const originalError = console.error;

      console.log = (message: any) => {
        addLog(String(message));
        originalLog(message);
      };
      console.warn = (message: any) => {
        addLog(`⚠️ ${String(message)}`);
        originalWarn(message);
      };
      console.error = (message: any) => {
        addLog(`❌ ${String(message)}`);
        originalError(message);
      };

      await runFullDiagnostic(session.user.id);

      // Restore original console methods
      console.log = originalLog;
      console.warn = originalWarn;
      console.error = originalError;

      addLog('✅ Diagnostic terminé');
    } catch (error) {
      addLog(`❌ Erreur pendant le diagnostic: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const testProfile = async () => {
    if (!session?.user?.id) {
      addLog('❌ Aucun utilisateur connecté');
      return;
    }

    setIsRunning(true);
    addLog('🧪 Test du profil utilisateur...');

    try {
      const profile = await fetchProfile(session.user.id);
      if (profile) {
        addLog(`✅ Profil trouvé: ${profile.name}`);
      } else {
        addLog('❌ Aucun profil trouvé');
      }
    } catch (error) {
      addLog(`❌ Erreur lors du test de profil: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const forceCreateProfile = async () => {
    if (!session?.user?.id) {
      addLog('❌ Aucun utilisateur connecté');
      return;
    }

    setIsRunning(true);
    addLog('🔧 Création forcée du profil...');

    try {
      const profile = await createProfileForUser(session.user.id, 'Utilisateur Test');
      if (profile) {
        addLog(`✅ Profil créé: ${profile.name}`);
      } else {
        addLog('❌ Échec de la création du profil');
      }
    } catch (error) {
      addLog(`❌ Erreur lors de la création forcée: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  if (!session?.user) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle>Diagnostic de la base de données</CardTitle>
        </CardHeader>
        <CardContent>
          <Text>Veuillez vous connecter pour utiliser le diagnostic.</Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Diagnostic de la base de données</CardTitle>
        <Text className="text-sm text-muted-foreground">
          Utilisateur: {session.user.email}
        </Text>
      </CardHeader>
      <CardContent className="gap-4">
        <View className="flex-row gap-2 flex-wrap">
          <Button 
            onPress={runDiagnostic} 
            disabled={isRunning}
            size="sm"
          >
            <Text>Diagnostic complet</Text>
          </Button>
          <Button 
            onPress={testProfile} 
            disabled={isRunning}
            variant="outline"
            size="sm"
          >
            <Text>Test profil</Text>
          </Button>
          <Button 
            onPress={forceCreateProfile} 
            disabled={isRunning}
            variant="secondary"
            size="sm"
          >
            <Text>Créer profil</Text>
          </Button>
          <Button 
            onPress={clearLogs} 
            disabled={isRunning}
            variant="destructive"
            size="sm"
          >
            <Text>Effacer logs</Text>
          </Button>
        </View>

        <ScrollView 
          className="h-64 bg-gray-100 p-2 rounded border"
          showsVerticalScrollIndicator={true}
        >
          {logs.map((log, index) => (
            <Text key={index} className="text-xs font-mono mb-1">
              {log}
            </Text>
          ))}
          {logs.length === 0 && (
            <Text className="text-muted-foreground text-sm">
              Aucun log pour le moment. Cliquez sur un bouton pour commencer.
            </Text>
          )}
        </ScrollView>
      </CardContent>
    </Card>
  );
}
