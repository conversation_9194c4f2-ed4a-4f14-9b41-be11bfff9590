import { Platform } from 'react-native';

// This file provides a polyfill for fetch in environments where it might not be available
// or where it needs to be customized

// For React Native, we don't need to do anything as fetch is already available
// For web, we use the global fetch

// This is just a placeholder to ensure the module is properly imported
export const setupFetchPolyfill = () => {
  if (Platform.OS === 'web') {
    // Web already has fetch
    return;
  }
  // React Native already has fetch
};

// Call the setup function immediately
setupFetchPolyfill();
