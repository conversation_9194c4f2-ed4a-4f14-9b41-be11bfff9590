import * as React from "react";
import {
  View,
  Pressable,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
} from "react-native";
import { Text } from "./text";
import { cn } from "~/lib/utils";
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from "react-native-reanimated";
import { X } from "lucide-react-native";

interface DrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

const Drawer = ({ open, onOpenChange, children }: DrawerProps) => {
  console.log("Drawer rendu avec open =", open);

  // Utiliser un état local pour suivre la visibilité
  const [isVisible, setIsVisible] = React.useState(false);

  // Synchroniser l'état local avec la prop open
  React.useEffect(() => {
    console.log("Drawer - useEffect - open a changé:", open);
    setIsVisible(open);
  }, [open]);

  // Afficher une alerte pour déboguer
  React.useEffect(() => {
    if (open) {
      console.log("Drawer ouvert - affichage de l'alerte de débogage");
      Alert.alert(
        "Drawer Debug",
        `Le drawer est ouvert avec open=${open} et isVisible=${isVisible}`,
        [{ text: "OK" }]
      );
    }
  }, [open, isVisible]);

  if (!open) {
    console.log("Drawer non rendu car open=false");
    return null;
  }

  console.log("Drawer rendu avec Modal visible");
  return (
    <Modal
      visible={true}
      transparent={true}
      animationType="slide"
      onRequestClose={() => {
        console.log("Modal onRequestClose appelé");
        onOpenChange(false);
      }}
    >
      <View style={StyleSheet.absoluteFill} className="flex-1 bg-black/50">
        <Pressable
          style={StyleSheet.absoluteFill}
          onPress={() => {
            console.log("Pressable de fond cliqué");
            onOpenChange(false);
          }}
        />
        <View className="absolute bottom-0 w-full bg-background rounded-t-xl">
          {children}
        </View>
      </View>
    </Modal>
  );
};

const DrawerTrigger = ({
  children,
  onPress,
}: {
  children: React.ReactNode;
  onPress: () => void;
}) => {
  return <Pressable onPress={onPress}>{children}</Pressable>;
};

const DrawerContent = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return <View className={cn("p-4", className)}>{children}</View>;
};

const DrawerHeader = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return <View className={cn("px-4 pt-4", className)}>{children}</View>;
};

const DrawerTitle = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Text className={cn("text-xl font-semibold", className)}>{children}</Text>
  );
};

const DrawerDescription = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Text className={cn("text-sm text-muted-foreground mt-1", className)}>
      {children}
    </Text>
  );
};

const DrawerFooter = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <View className={cn("flex flex-row justify-end gap-2 p-4", className)}>
      {children}
    </View>
  );
};

const DrawerClose = ({
  children,
  onPress,
}: {
  children: React.ReactNode;
  onPress: () => void;
}) => {
  return <Pressable onPress={onPress}>{children}</Pressable>;
};

export {
  Drawer,
  DrawerTrigger,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
};
