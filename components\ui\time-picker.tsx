// --- Begin components/ui/time-picker.tsx ---
import React, { useState } from "react";
import { View, TextInput, Platform } from "react-native";
import { Button } from "./button";
import { Text } from "./text";
import { Label } from "./label";
import { cn } from "~/lib/utils";
import { ClockIcon } from "lucide-react-native";

interface TimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function TimePicker({
  value,
  onChange,
  label,
  className,
  disabled = false,
}: TimePickerProps) {
  // Extraire les heures et minutes de la date
  const hours = value.getHours();
  const minutes = value.getMinutes();
  
  // Formater pour l'affichage
  const formattedHours = hours.toString().padStart(2, "0");
  const formattedMinutes = minutes.toString().padStart(2, "0");
  
  // État local pour l'édition
  const [hoursInput, setHoursInput] = useState(formattedHours);
  const [minutesInput, setMinutesInput] = useState(formattedMinutes);
  
  // Fonction pour mettre à jour l'heure
  const updateTime = (newHours: string, newMinutes: string) => {
    const h = parseInt(newHours, 10);
    const m = parseInt(newMinutes, 10);
    
    if (isNaN(h) || isNaN(m) || h < 0 || h > 23 || m < 0 || m > 59) {
      return;
    }
    
    const newDate = new Date(value);
    newDate.setHours(h, m);
    onChange(newDate);
  };
  
  // Gérer le changement des heures
  const handleHoursChange = (text: string) => {
    const cleanText = text.replace(/[^0-9]/g, "").slice(0, 2);
    setHoursInput(cleanText);
    
    if (cleanText.length === 2) {
      updateTime(cleanText, minutesInput);
    }
  };
  
  // Gérer le changement des minutes
  const handleMinutesChange = (text: string) => {
    const cleanText = text.replace(/[^0-9]/g, "").slice(0, 2);
    setMinutesInput(cleanText);
    
    if (cleanText.length === 2) {
      updateTime(hoursInput, cleanText);
    }
  };
  
  return (
    <View className={cn("flex flex-col", className)}>
      {label && (
        <Label className="mb-1.5 font-medium">
          {label}
        </Label>
      )}
      
      <View className="flex flex-row items-center border rounded-md p-2 bg-background">
        <ClockIcon size={18} className="mr-2 text-muted-foreground" />
        
        <View className="flex flex-row items-center">
          <TextInput
            className="w-8 text-center text-foreground"
            value={hoursInput}
            onChangeText={handleHoursChange}
            keyboardType="numeric"
            maxLength={2}
            placeholder="00"
            editable={!disabled}
          />
          <Text className="mx-1">:</Text>
          <TextInput
            className="w-8 text-center text-foreground"
            value={minutesInput}
            onChangeText={handleMinutesChange}
            keyboardType="numeric"
            maxLength={2}
            placeholder="00"
            editable={!disabled}
          />
        </View>
      </View>
    </View>
  );
}
// --- End components/ui/time-picker.tsx ---
