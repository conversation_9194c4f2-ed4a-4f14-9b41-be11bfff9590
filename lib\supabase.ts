// --- Begin lib/supabase.ts ---
import { AppState, Platform } from "react-native";
import "react-native-url-polyfill/auto";
import { createClient, SupabaseClient } from "@supabase/supabase-js"; // Import SupabaseClient
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Database } from "./database.types"; // Import generated types

// Charger les variables d'environnement via le mécanisme intégré d'Expo
const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL ||
  "https://your-supabase-url.supabase.co";
const supabaseAnonKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "your-anon-key";
const supabaseServiceKey =
  process.env.EXPO_PUBLIC_SUPABASE_SERVICE_KEY || "your-service-key";

// Vérification que les clés sont bien chargées
if (supabaseUrl === "https://your-supabase-url.supabase.co") {
  console.warn(
    "Using default Supabase URL. Set EXPO_PUBLIC_SUPABASE_URL in your .env file for production."
  );
}
if (supabaseAnonKey === "your-anon-key") {
  console.warn(
    "Using default Supabase Anon Key. Set EXPO_PUBLIC_SUPABASE_ANON_KEY in your .env file for production."
  );
}

// Create Supabase client based on platform
// Explicitly type the variables
let supabase: SupabaseClient<Database>;
let supabaseAdmin: SupabaseClient<Database>;

// For native platforms
if (Platform.OS !== "web") {
  // Client standard avec la clé anonyme
  supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
    // Add Database type generic
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  });

  // Client admin avec la clé de service pour contourner les politiques RLS
  supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey, {
    // Add Database type generic
    auth: {
      persistSession: false,
    },
  });
} else {
  // For web platform - import from supabaseWeb.ts
  const { supabaseWeb, supabaseAdminWeb } = require("./supabaseWeb");
  supabase = supabaseWeb;
  supabaseAdmin = supabaseAdminWeb;
}

export { supabase, supabaseAdmin };

// Tells Supabase Auth to continuously refresh the session automatically
// if the app is in the foreground. When this is added, you will continue
// to receive `onAuthStateChange` events with the `TOKEN_REFRESHED` or
// `SIGNED_OUT` event if the user's session is terminated. This should
// only be registered once.
if (Platform.OS !== "web") {
  AppState.addEventListener("change", (state) => {
    if (state === "active") {
      supabase.auth.startAutoRefresh();
    } else {
      supabase.auth.stopAutoRefresh();
    }
  });
}
// --- End lib/supabase.ts ---
