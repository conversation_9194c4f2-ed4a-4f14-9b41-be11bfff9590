// --- Begin app/(tabs)/tasks.tsx ---
import React, { useState, useEffect, useCallback } from "react";
import { View, FlatList, ActivityIndicator, Pressable } from "react-native";
import { Text } from "~/components/ui/text";
import { Checkbox } from "~/components/ui/checkbox";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button"; // Ajout Button pour retry
import { useAuth } from "~/lib/AuthContext";
import { fetchItemsAssignedToUser } from "~/lib/supabaseCrud";
import { Item, CostEnum, EffortEnum, Event } from "~/lib/types"; // Ajout Event type
import { useFocusEffect } from "expo-router";
import { showToast } from "~/lib/toastService";
import {
  ShoppingCart,
  CookingPot,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Tod<PERSON>,
} from "lucide-react-native";
import { Separator } from "~/components/ui/separator";

interface GroupedTasks {
  [eventId: number]: {
    eventTitle: string;
    eventIcon?: string | null;
    tasks: Item[];
  };
}

export default function UserTasksScreen() {
  const { session } = useAuth();
  const [tasks, setTasks] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Correction: Utilisation du pattern correct pour useFocusEffect avec async
  useFocusEffect(
    useCallback(() => {
      const loadTasks = async () => {
        if (!session?.user?.id) {
          setTasks([]);
          setLoading(false);
          setError(null);
          return;
        }
        setLoading(true);
        setError(null);
        try {
          // Note: Ensure the foreign key relationship between items.assigned_participant_id
          // and participants.id is correctly set up in Supabase.
          const assignedItems = await fetchItemsAssignedToUser(session.user.id);
          setTasks(assignedItems || []);
        } catch (e: any) {
          console.error("Error loading user tasks:", e);
          if (
            e?.message?.includes("relationship") &&
            e?.message?.includes("does not exist")
          ) {
            setError(
              "Erreur de configuration (relation manquante). Veuillez vérifier la base de données."
            );
          } else {
            setError("Impossible de charger vos tâches.");
          }
          setTasks([]);
        } finally {
          setLoading(false);
        }
      };

      loadTasks(); // Appelle la fonction async
    }, [session?.user?.id]) // Dépendances du useCallback
  );

  // Grouper les tâches par événement
  const groupedTasks = tasks.reduce((acc, task) => {
    // Vérifier que l'événement est bien chargé (relation via fetchItemsAssignedToUser)
    const eventInfo = task.event as Event | undefined; // Cast avec vérification potentielle
    if (!eventInfo) {
      console.warn(`Task ${task.id} is missing event details.`);
      return acc;
    }

    const eventId = eventInfo.id;
    if (!acc[eventId]) {
      acc[eventId] = {
        eventTitle: eventInfo.title,
        eventIcon: eventInfo.icon,
        tasks: [],
      };
    }
    acc[eventId].tasks.push(task);
    return acc;
  }, {} as GroupedTasks);

  // Convertir l'objet groupé en tableau pour FlatList
  const taskSections = Object.entries(groupedTasks).map(([eventId, data]) => ({
    eventId: parseInt(eventId, 10),
    title: data.eventTitle,
    icon: data.eventIcon,
    data: data.tasks,
  }));

  const renderTaskItem = ({ item }: { item: Item }) => {
    const [isChecked, setIsChecked] = useState(false);

    return (
      <View className="flex-row items-center py-3 px-2 space-x-3 border-b border-border/30 last:border-b-0">
        <Checkbox
          aria-label={`Marquer ${item.name} comme fait`}
          checked={isChecked}
          onCheckedChange={setIsChecked}
        />
        <View className="flex-1">
          <Text className="text-base text-foreground">{item.name}</Text>
          <View className="flex-row gap-1.5 items-center mt-1">
            {item.estimated_cost && (
              <Badge variant="outline" className="py-0.5 px-1">
                {item.estimated_cost}
              </Badge>
            )}
            {item.estimated_effort === EffortEnum.Low && (
              <ShoppingCart size={14} className="text-muted-foreground" />
            )}
            {item.estimated_effort === EffortEnum.Medium && (
              <CookingPot size={14} className="text-muted-foreground" />
            )}
            {item.estimated_effort === EffortEnum.High && (
              <UserCheck size={14} className="text-muted-foreground" />
            )}
          </View>
        </View>
      </View>
    );
  };

  if (!session && !loading) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-center text-muted-foreground">
          Connectez-vous pour voir vos tâches.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" className="text-primary" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background gap-4">
        <Text className="text-lg text-destructive text-center">{error}</Text>
        {/* Correction: Mettre loadTasks dans le onPress */}
        <Button onPress={loadTasks}>
          <Text>Réessayer</Text>
        </Button>
      </View>
    );
  }

  return (
    <View className="flex-1 p-4 bg-background">
      {tasks.length === 0 ? (
        <View className="flex-1 justify-center items-center gap-2">
          <ListTodo size={48} className="text-muted-foreground mb-4" />
          <Text className="text-xl font-semibold text-foreground">
            Aucune tâche assignée
          </Text>
          <Text className="text-muted-foreground text-center">
            Vos items à apporter apparaîtront ici.
          </Text>
        </View>
      ) : (
        <FlatList
          data={taskSections}
          keyExtractor={(section) => section.eventId.toString()}
          renderItem={({ item: section }) => (
            <Card className="mb-4 border border-border">
              <CardHeader className="flex-row items-center pb-2 pt-3 px-4">
                <Text className="text-2xl mr-2">{section.icon || "🎉"}</Text>
                <CardTitle className="text-lg font-semibold">
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pt-0">
                {section.data.map((taskItem) => (
                  <View key={taskItem.id} className="px-4">
                    {renderTaskItem({ item: taskItem })}
                  </View>
                ))}
              </CardContent>
            </Card>
          )}
        />
      )}
    </View>
  );
}
// --- End app/(tabs)/tasks.tsx ---
