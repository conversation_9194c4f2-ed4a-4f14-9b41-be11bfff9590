import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

// Minimal buttonTextVariants to satisfy components expecting it
// This may need further refinement if specific text styling is required
// based on button variant and size, beyond what buttonVariants provides.
const buttonTextVariants = cva("", {
  // No base classes, just variants if needed
  variants: {
    variant: {
      default: "", // text-primary-foreground is on the button itself
      destructive: "", // text-destructive-foreground is on the button itself
      outline: "", // text-accent-foreground is on hover by the button
      secondary: "", // text-secondary-foreground is on the button itself
      ghost: "", // text-accent-foreground is on hover by the button
      link: "", // text-primary is on the button itself
    },
    size: {
      default: "",
      sm: "",
      lg: "",
      icon: "", // Icons usually don't have text that needs this
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  },
});

export type ButtonTextVariantProps = VariantProps<typeof buttonTextVariants>;

export { Button, buttonVariants, buttonTextVariants };
