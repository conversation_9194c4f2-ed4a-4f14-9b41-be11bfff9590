// Web-specific entry point
// Import polyfills first
import "react-native-url-polyfill/auto";
import "./lib/fetchPolyfill";
import "./lib/textEncodingPolyfill";

import { registerRootComponent } from "expo";
import { ExpoRoot } from "expo-router";

// Must be exported or Fast Refresh won't update the context
export function App() {
  const ctx = require.context("./app");
  return <ExpoRoot context={ctx} />;
}

registerRootComponent(App);
