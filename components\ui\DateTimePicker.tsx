// --- Begin components/ui/DateTimePicker.tsx ---
import React, { useState } from "react";
import { Platform, Pressable, View } from "react-native";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import { Popover, PopoverContent, PopoverTrigger } from "./popover"; // Assurez-vous que Popover est bien exporté
import { Button } from "./button";
import { Text } from "./text";
import { CalendarIcon, ClockIcon } from "lucide-react-native"; // Assurez-vous d'avoir ces icônes
import { cn } from "~/lib/utils";

interface DateTimePickerProps {
  mode: "date" | "time";
  value: Date;
  onChange: (event: DateTimePickerEvent, date?: Date) => void;
  triggerLabel?: string;
  triggerClassName?: string;
  disabled?: boolean;
}

export function CustomDateTimePicker({
  mode,
  value,
  onChange,
  triggerLabel,
  triggerClassName,
  disabled = false,
}: DateTimePickerProps) {
  const [open, setOpen] = useState(false);

  const handleValueChange = (event: DateTimePickerEvent, date?: Date) => {
    if (Platform.OS === "android") {
      setOpen(false); // Close popover on Android after selection
    }
    onChange(event, date);
  };

  const displayValue =
    mode === "date"
      ? value.toLocaleDateString()
      : value.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

  const Icon = mode === "date" ? CalendarIcon : ClockIcon;

  // Android doesn't use Popover, shows native modal directly
  if (Platform.OS === "android") {
    return (
      <View>
        {open && (
          <DateTimePicker
            value={value}
            mode={mode}
            display="default" // Ou "spinner"
            onChange={handleValueChange}
          />
        )}
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            triggerClassName
          )}
          onPress={() => setOpen(true)}
          disabled={disabled}
        >
          {/* <Icon className="mr-2 h-4 w-4 text-muted-foreground" size={16} /> */}
          <Text className={!value ? "text-muted-foreground" : ""}>
            {triggerLabel ? triggerLabel + ": " : ""}
            {displayValue}
          </Text>
        </Button>
      </View>
    );
  }

  // iOS and Web use Popover
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            triggerClassName
          )}
          disabled={disabled}
        >
          <Icon className="mr-2 h-4 w-4 text-muted-foreground" size={16} />
          <Text className={!value ? "text-muted-foreground" : ""}>
            {triggerLabel ? triggerLabel + ": " : ""}
            {displayValue}
          </Text>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        {/* iOS uses a compact inline picker */}
        <DateTimePicker
          value={value}
          mode={mode}
          display={Platform.OS === "ios" ? "inline" : "default"} // Web might need 'default' or specific styling
          onChange={handleValueChange}
        />
        {/* Optional: Add a close button for Web/iOS inside the Popover if needed */}
        {Platform.OS !== "ios" && (
          <View className="p-2 border-t border-border">
            <Button onPress={() => setOpen(false)} size="sm">
              <Text>Close</Text>
            </Button>
          </View>
        )}
      </PopoverContent>
    </Popover>
  );
}
// --- End components/ui/DateTimePicker.tsx ---
