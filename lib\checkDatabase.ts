// Script pour vérifier l'état de la base de données
import { supabaseAdmin } from './supabase';

export async function checkDatabaseTables() {
  console.log('🔍 Vérification des tables de la base de données...');
  
  const tables = ['profiles', 'events', 'participants', 'items', 'contacts', 'contact_groups', 'contact_group_members'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabaseAdmin
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Erreur pour la table ${table}:`, error.message);
      } else {
        console.log(`✅ Table ${table}: OK`);
      }
    } catch (e) {
      console.error(`❌ Exception pour la table ${table}:`, e);
    }
  }
}

export async function checkUserProfile(userId: string) {
  console.log(`🔍 Vérification du profil utilisateur ${userId}...`);
  
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId);
    
    if (error) {
      console.error('❌ Erreur lors de la vérification du profil:', error.message);
      return false;
    }
    
    if (!data || data.length === 0) {
      console.log('⚠️ Aucun profil trouvé pour cet utilisateur');
      return false;
    }
    
    console.log('✅ Profil trouvé:', data[0]);
    return true;
  } catch (e) {
    console.error('❌ Exception lors de la vérification du profil:', e);
    return false;
  }
}

export async function createUserProfileIfMissing(userId: string, name: string = 'Utilisateur') {
  console.log(`🔧 Création du profil utilisateur ${userId}...`);
  
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: userId,
        name: name,
        avatar_url: null,
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Erreur lors de la création du profil:', error.message);
      return null;
    }
    
    console.log('✅ Profil créé avec succès:', data);
    return data;
  } catch (e) {
    console.error('❌ Exception lors de la création du profil:', e);
    return null;
  }
}
