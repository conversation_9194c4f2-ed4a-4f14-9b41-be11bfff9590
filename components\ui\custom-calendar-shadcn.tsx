"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react-native";
import { DayPicker } from "react-day-picker";
import { fr } from "date-fns/locale";
import { cn } from "~/lib/utils";
import { isSameMonth } from "date-fns";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function CustomCalendarShadcn({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  // Obtenir la date actuelle pour limiter la navigation
  const today = new Date();
  const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  // État pour suivre le mois actuellement affiché
  const [currentView, setCurrentView] = React.useState<Date>(currentMonth);

  // Fonction pour vérifier si on est sur le mois actuel
  const isCurrentMonth = React.useMemo(() => {
    return isSameMonth(currentView, currentMonth);
  }, [currentView, currentMonth]);

  return (
    <DayPicker
      locale={fr}
      showOutsideDays={showOutsideDays}
      className={cn("p-3 bg-white rounded-lg", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-base font-medium text-foreground",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          "h-9 w-9 bg-transparent p-0 opacity-70 hover:opacity-100 flex items-center justify-center rounded-full"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell: "text-muted-foreground w-10 font-medium text-[0.8rem] py-2",
        row: "flex w-full mt-2",
        cell: "h-10 w-10 text-center text-sm p-0 relative focus-within:relative focus-within:z-20",
        day: cn(
          "h-10 w-10 p-0 font-normal aria-selected:opacity-100 rounded-full flex items-center justify-center hover:bg-gray-100"
        ),
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground font-semibold",
        day_outside: "day-outside text-muted-foreground opacity-50",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: () =>
          isCurrentMonth ? null : <ChevronLeft className="h-5 w-5" />,
        IconRight: () => <ChevronRight className="h-5 w-5" />,
      }}
      fromMonth={currentMonth} // Empêche de naviguer vers les mois précédents
      onMonthChange={setCurrentView} // Mettre à jour le mois actuellement affiché
      {...props}
    />
  );
}
CustomCalendarShadcn.displayName = "CustomCalendarShadcn";

export { CustomCalendarShadcn };
