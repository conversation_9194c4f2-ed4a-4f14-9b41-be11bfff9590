// Script de test pour vérifier les fonctions CRUD
import { 
  fetchProfile, 
  createProfile, 
  fetchEventsForUser, 
  fetchItemsAssignedToUser 
} from './supabaseCrud';

export async function testCrudFunctions(userId: string) {
  console.log('🧪 Début des tests CRUD...');
  
  try {
    // Test 1: Fetch Profile
    console.log('📋 Test 1: fetchProfile');
    const profile = await fetchProfile(userId);
    console.log('✅ Profile récupéré:', profile ? 'Succès' : 'Aucun profil trouvé');
    
    // Test 2: Fetch Events
    console.log('📋 Test 2: fetchEventsForUser');
    const events = await fetchEventsForUser(userId);
    console.log('✅ Événements récupérés:', events.length, 'événements trouvés');
    
    // Test 3: Fetch Items Assigned
    console.log('📋 Test 3: fetchItemsAssignedToUser');
    const items = await fetchItemsAssignedToUser(userId);
    console.log('✅ Items assignés récupérés:', items.length, 'items trouvés');
    
    console.log('🎉 Tous les tests CRUD terminés avec succès !');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors des tests CRUD:', error);
    return false;
  }
}

// Fonction utilitaire pour tester la création de profil
export async function testCreateProfile(userId: string, name: string) {
  console.log('🧪 Test de création de profil...');
  
  try {
    const newProfile = await createProfile(userId, {
      name: name,
      avatar_url: null,
    });
    
    if (newProfile) {
      console.log('✅ Profil créé avec succès:', newProfile);
      return true;
    } else {
      console.log('❌ Échec de la création du profil');
      return false;
    }
  } catch (error) {
    console.error('❌ Erreur lors de la création du profil:', error);
    return false;
  }
}
