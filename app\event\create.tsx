import React, { useState, useEffect } from "react";
import { View, ScrollView, Alert, Platform } from "react-native";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { CustomButton } from "~/components/ui/custom-button";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { TimePicker } from "~/components/ui/time-picker";
import { ShadcnDatePicker } from "~/components/ui/shadcn-date-picker";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog"; // Import Dialog components
import { EmojiPicker, PickerEmoji } from "~/components/EmojiPicker"; // Import EmojiPicker
// Icône temporairement supprimée pour compatibilité web
import { cn } from "~/lib/utils"; // Import cn utility

import { useAuth } from "~/lib/AuthContext";
import { createEvent, createParticipant } from "~/lib/supabaseCrud";
import { createEventDirect, createParticipantDirect } from "~/lib/directApi";
import { useRouter } from "expo-router";
import { showToast } from "~/lib/toastService";
import { Switch } from "~/components/ui/switch";
// Assuming ParticipantRole and ParticipantStatus are now string literal unions from database.types.ts
// If they are still enums from lib/types.ts, adjust import if needed.
// For now, assuming they are string literals as per recent refactoring.
// import { ParticipantRole, ParticipantStatus } from "~/lib/types";
type ParticipantRoleEnum = Database["public"]["Enums"]["participant_role"];
type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];
import { Database } from "~/lib/database.types";

// Interface pour les erreurs de validation
interface ValidationErrors {
  title?: string;
  dateTime?: string;
}

export default function CreateEventScreen() {
  const { session } = useAuth();
  const router = useRouter();

  // États du formulaire
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dateTime, setDateTime] = useState(new Date());
  const [location, setLocation] = useState("");
  const [icon, setIcon] = useState(""); // Will store the native emoji string
  const [allowSuggestions, setAllowSuggestions] = useState(false);
  const [allowPreAssignment, setAllowPreAssignment] = useState(false);

  // États UI
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);
  const [showEmojiPickerDialog, setShowEmojiPickerDialog] = useState(false);

  useEffect(() => {
    if (submitAttempted) {
      validateForm();
    }
  }, [title, dateTime, submitAttempted]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!title.trim()) {
      newErrors.title = "Le titre est obligatoire";
    }
    const now = new Date();
    now.setSeconds(0, 0);
    const dateToCheck = new Date(dateTime);
    dateToCheck.setSeconds(0, 0);
    const isSameDay =
      dateToCheck.getDate() === now.getDate() &&
      dateToCheck.getMonth() === now.getMonth() &&
      dateToCheck.getFullYear() === now.getFullYear();
    if (isSameDay) {
      const nowTime = now.getHours() * 60 + now.getMinutes() - 5;
      const eventTime = dateToCheck.getHours() * 60 + dateToCheck.getMinutes();
      if (eventTime <= nowTime) {
        newErrors.dateTime =
          "Pour aujourd'hui, l'heure doit être dans le futur";
      }
    } else if (dateToCheck < now) {
      newErrors.dateTime = "La date ne peut pas être dans le passé";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconSelect = (selectedEmoji: PickerEmoji) => {
    setIcon(selectedEmoji.native);
    setShowEmojiPickerDialog(false); // Close the dialog
  };

  const handleCreateEvent = async () => {
    setSubmitAttempted(true);
    setCreationError(null);
    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour créer un événement.", {
        type: "error",
      });
      return;
    }
    if (!validateForm()) {
      Alert.alert(
        "Formulaire incomplet",
        "Veuillez corriger les erreurs dans le formulaire."
      );
      return;
    }

    try {
      setLoading(true);
      const eventData = {
        title,
        description: description || null,
        date_time: dateTime.toISOString(),
        location: location || null,
        icon: icon || null,
        allow_suggestions: allowSuggestions,
        allow_pre_assignment: allowPreAssignment,
        organizer_delegated: false,
      };

      let createdEvent;
      try {
        createdEvent = await createEventDirect(eventData, session.user.id);
      } catch (directError) {
        console.error(
          "Échec avec l'API directe, tentative avec la méthode standard...",
          directError
        );
        createdEvent = await createEvent(eventData, session.user.id);
      }

      if (!createdEvent) {
        setCreationError(
          "Impossible de créer l'événement. Veuillez réessayer."
        );
        showToast("Erreur lors de la création de l'événement.", {
          type: "error",
        });
        setLoading(false);
        return;
      }

      const participantData = {
        event_id: createdEvent.id,
        user_id: session.user.id,
        role: "organizer" as ParticipantRoleEnum, // Use string literal
        status: "accepted" as ParticipantStatusEnum, // Use string literal
        anonymous_name: null,
        anonymous_email: null,
        anonymous_phone: null,
        invitation_token: null,
      };

      let participantResult;
      try {
        participantResult = await createParticipantDirect(participantData);
      } catch (directError) {
        console.error(
          "Échec ajout participant (direct), tentative standard...",
          directError
        );
        participantResult = await createParticipant(participantData);
      }

      if (participantResult) {
        showToast("Événement créé avec succès !", { type: "success" });
        setTitle("");
        setDescription("");
        setDateTime(new Date());
        setLocation("");
        setIcon("");
        setAllowSuggestions(false);
        setAllowPreAssignment(false);
        setSubmitAttempted(false);
        router.back();
      } else {
        setCreationError(
          "L'événement a été créé mais vous n'avez pas été ajouté comme organisateur."
        );
        showToast(
          "Événement créé, mais erreur lors de l'ajout de l'organisateur.",
          { type: "error" }
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      setCreationError(`Une erreur est survenue: ${errorMessage}`);
      showToast(`Erreur inattendue: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-red-500 mt-1">{message}</Text>;
  };

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "#f9fafb" }}
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        <View className="mb-8">
          <Text className="text-2xl font-bold text-center mb-2">
            Nouvel Événement
          </Text>
          <Text className="text-gray-500 text-center">
            Créez votre événement en quelques étapes
          </Text>
        </View>

        {creationError && (
          <View className="p-4 bg-red-100 border border-red-300 rounded-lg mb-6">
            <Text className="text-red-700">{creationError}</Text>
          </View>
        )}

        <View
          className={`bg-white rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4">
            Informations principales
          </Text>
          <View className="mb-5">
            <Label nativeID="titleLabel" className="flex-row mb-1.5">
              <Text className="text-red-500 mr-1">*</Text>
              <Text className="font-medium">Titre</Text>
            </Label>
            <Input
              nativeID="titleLabel"
              placeholder="Ex: Anniversaire de Gauthier"
              value={title}
              onChangeText={setTitle}
              aria-required="true"
              className={`h-11 ${
                errors.title ? "border-red-500" : "border-gray-200"
              }`}
            />
            <ErrorMessage message={errors.title} />
          </View>
          <View className="mb-5">
            <Label nativeID="descLabel" className="mb-1.5 font-medium">
              Description
            </Label>
            <Textarea
              nativeID="descLabel"
              placeholder="Détails supplémentaires (thème, code vestimentaire...)"
              value={description}
              onChangeText={setDescription}
              numberOfLines={3}
              className="border-gray-200"
            />
          </View>
          <View className="mb-2">
            <Label nativeID="dateTimeLabel" className="flex-row mb-1.5">
              <Text className="text-red-500 mr-1">*</Text>
              <Text className="font-medium">Date et Heure</Text>
            </Label>
            <View
              className={Platform.OS === "web" ? "flex-row gap-4" : "gap-4"}
            >
              <View className={Platform.OS === "web" ? "flex-1" : "mb-4"}>
                <ShadcnDatePicker
                  date={dateTime}
                  setDate={(date) => {
                    if (date) {
                      const newDate = new Date(date);
                      newDate.setHours(
                        dateTime.getHours(),
                        dateTime.getMinutes()
                      );
                      setDateTime(newDate);
                    }
                  }}
                  error={errors.dateTime}
                  minDate={new Date()}
                  label="Date"
                />
              </View>
              <View className={Platform.OS === "web" ? "flex-1" : ""}>
                <TimePicker
                  value={dateTime}
                  onChange={(date) => {
                    const newDate = new Date(dateTime);
                    newDate.setHours(date.getHours(), date.getMinutes());
                    setDateTime(newDate);
                  }}
                  label="Heure"
                />
              </View>
            </View>
            <ErrorMessage message={errors.dateTime} />
          </View>
        </View>

        <View
          className={`bg-white rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4">
            Détails supplémentaires
          </Text>
          <View className="mb-5">
            <Label nativeID="locationLabel" className="mb-1.5 font-medium">
              Lieu
            </Label>
            <Input
              nativeID="locationLabel"
              placeholder="Ex: 12 Rue de la Paix, Paris"
              value={location}
              onChangeText={setLocation}
              className="h-11 border-gray-200"
            />
          </View>

          {/* Icône (Emoji Picker) */}
          <View className="mb-5">
            <Label nativeID="iconLabel" className="mb-1.5 font-medium">
              Icône
            </Label>
            <Button
              variant="outline"
              className="h-12 w-full flex-row items-center justify-between px-3 py-2"
              onPress={() => setShowEmojiPickerDialog(true)}
            >
              <Text
                className={cn(
                  "text-sm",
                  icon ? "text-foreground" : "text-muted-foreground"
                )}
              >
                {icon || "Choisir une icône"}
              </Text>
              {icon ? (
                <Text className="text-2xl">{icon}</Text>
              ) : (
                <Text className="text-muted-foreground text-xl">😊</Text>
              )}
            </Button>
            <Dialog
              open={showEmojiPickerDialog}
              onOpenChange={setShowEmojiPickerDialog}
            >
              <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none">
                {" "}
                {/* Further Increased max-width to md */}{" "}
                {/* DialogContent might need specific styling for picker */}
                <EmojiPicker onEmojiSelected={handleIconSelect} />
              </DialogContent>
            </Dialog>
          </View>
        </View>

        <View
          className={`bg-white rounded-xl shadow-sm mb-8 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4">Options</Text>
          <View className="space-y-4">
            <View className="flex-row items-center justify-between p-3 bg-gray-50 rounded-lg">
              <Label nativeID="suggestionsLabel" className="flex-1 mr-2">
                Autoriser les suggestions d'items par les invités ?
              </Label>
              <Switch
                nativeID="suggestionsLabel"
                checked={allowSuggestions}
                onCheckedChange={setAllowSuggestions}
              />
            </View>
            <View className="flex-row items-center justify-between p-3 bg-gray-50 rounded-lg">
              <Label nativeID="preassignLabel" className="flex-1 mr-2">
                Autoriser les invités à "fixer" des items à l'avance ?
              </Label>
              <Switch
                nativeID="preassignLabel"
                checked={allowPreAssignment}
                onCheckedChange={setAllowPreAssignment}
              />
            </View>
          </View>
        </View>

        <CustomButton
          onPress={handleCreateEvent}
          disabled={loading}
          loading={loading}
          className="h-12 bg-primary rounded-lg mb-4"
        >
          <Text className="text-white font-medium text-center w-full">
            {loading ? "Création en cours..." : "Créer l'Événement"}
          </Text>
        </CustomButton>
        <Text className="text-sm text-gray-500 text-center mb-8">
          <Text className="text-red-500">*</Text> Champs obligatoires
        </Text>
      </View>
    </ScrollView>
  );
}
