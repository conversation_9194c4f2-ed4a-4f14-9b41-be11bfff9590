import React, { useState, useEffect } from "react";
import { View, FlatList, RefreshControl, Alert } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { H1 } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
// Icônes temporairement supprimées pour compatibilité web
import { useAuth } from "~/lib/AuthContext";
import { fetchEventsForUser } from "~/lib/supabaseCrud";
import { Event } from "~/lib/types";

export default function EventsScreen() {
  const router = useRouter();
  const { session } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const loadEvents = async () => {
    if (!session?.user?.id) return;

    setLoading(true);
    try {
      const fetchedEvents = await fetchEventsForUser(session.user.id);
      setEvents(fetchedEvents);
    } catch (error) {
      console.error("Erreur lors du chargement des événements:", error);
      Alert.alert("Erreur", "Impossible de charger les événements");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEvents();
    setRefreshing(false);
  };

  useEffect(() => {
    loadEvents();
  }, [session?.user?.id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderEventCard = ({ item: event }: { item: Event }) => (
    <Card className="mb-4 border border-border bg-white">
      <CardHeader className="pb-2">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center gap-3">
            <Text className="text-3xl">{event.icon || "🎉"}</Text>
            <View className="flex-1">
              <CardTitle className="text-lg font-semibold text-foreground">
                {event.title}
              </CardTitle>
              {event.description && (
                <Text className="text-sm text-muted-foreground mt-1">
                  {event.description}
                </Text>
              )}
            </View>
          </View>
          <Badge variant="secondary" className="ml-2">
            <Text>
              {event.organizer_id === session?.user?.id
                ? "Organisateur"
                : "Invité"}
            </Text>
          </Badge>
        </View>
      </CardHeader>
      <CardContent className="gap-2">
        <View className="flex-row items-center gap-2">
          <Text className="text-muted-foreground">📅</Text>
          <Text className="text-sm text-foreground">
            {formatDate(event.date_time)}
          </Text>
        </View>
        <View className="flex-row items-center gap-2">
          <Text className="text-muted-foreground">🕐</Text>
          <Text className="text-sm text-foreground">
            {formatTime(event.date_time)}
          </Text>
        </View>
        {event.location && (
          <View className="flex-row items-center gap-2">
            <Text className="text-muted-foreground">📍</Text>
            <Text className="text-sm text-foreground">{event.location}</Text>
          </View>
        )}
        <View className="flex-row items-center justify-between mt-3">
          <View className="flex-row items-center gap-2">
            <Text className="text-muted-foreground">👥</Text>
            <Text className="text-sm text-muted-foreground">Participants</Text>
          </View>
          <Button
            variant="outline"
            size="sm"
            onPress={() => router.push(`/event/${event.id}`)}
          >
            <Text>Voir détails</Text>
          </Button>
        </View>
      </CardContent>
    </Card>
  );

  if (!session) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background">
        <Text className="text-lg text-muted-foreground text-center">
          Veuillez vous connecter pour voir vos événements
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background">
      <View className="p-4 pb-2">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <H1 className="text-2xl font-bold text-foreground">
              Mes Événements
            </H1>
            <Text className="text-muted-foreground">
              {events.length} événement{events.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <Button onPress={() => router.push("/event/create")} size="sm">
            <Text>➕ Créer</Text>
          </Button>
        </View>
      </View>

      {events.length === 0 ? (
        <View className="flex-1 justify-center items-center gap-4 p-4">
          <View className="bg-muted/20 p-6 rounded-full">
            <Text className="text-6xl">📅</Text>
          </View>
          <View className="items-center gap-2">
            <Text className="text-xl font-semibold text-foreground">
              Aucun événement
            </Text>
            <Text className="text-muted-foreground text-center max-w-sm">
              Créez votre premier événement pour commencer à organiser vos fêtes
              !
            </Text>
          </View>
          <Button onPress={() => router.push("/event/create")} className="mt-4">
            <Text>➕ Créer mon premier événement</Text>
          </Button>
        </View>
      ) : (
        <FlatList
          data={events}
          renderItem={renderEventCard}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ padding: 16, paddingTop: 0 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}
