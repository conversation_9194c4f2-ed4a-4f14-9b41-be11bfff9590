// This file provides a polyfill for TextEncoder/TextDecoder in environments where it might not be available

import { Platform } from 'react-native';

export const setupTextEncodingPolyfill = () => {
  if (Platform.OS === 'web') {
    // Web already has TextEncoder/TextDecoder
    return;
  }

  try {
    // Check if TextEncoder/TextDecoder are already available
    if (typeof TextEncoder === 'undefined' || typeof TextDecoder === 'undefined') {
      // If not available, try to import them from text-encoding
      const textEncoding = require('text-encoding');
      
      if (typeof global !== 'undefined') {
        if (typeof global.TextEncoder === 'undefined') {
          global.TextEncoder = textEncoding.TextEncoder;
        }
        if (typeof global.TextDecoder === 'undefined') {
          global.TextDecoder = textEncoding.TextDecoder;
        }
      }
    }
  } catch (error) {
    console.warn('Failed to polyfill TextEncoder/TextDecoder:', error);
  }
};

// Call the setup function immediately
setupTextEncodingPolyfill();
