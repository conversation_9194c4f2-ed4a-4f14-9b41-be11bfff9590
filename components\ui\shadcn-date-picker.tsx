"use client";

import * as React from "react";
import { format, isBefore, isAfter } from "date-fns";
import { fr } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react-native";
import { View, Text, Pressable, Platform } from "react-native";

import { CustomCalendarShadcn } from "~/components/ui/custom-calendar-shadcn";
import { Modal } from "~/components/ui/modal";

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  error?: string;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
}

export function ShadcnDatePicker({
  date,
  setDate,
  label,
  placeholder = "Sélectionner une date",
  className = "",
  error,
  minDate,
  maxDate,
  disabled = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);

  const formattedDate = date
    ? format(date, "dd MMMM yyyy", { locale: fr })
    : placeholder;

  // Pour le web uniquement
  if (Platform.OS === "web") {
    return (
      <View className={`${className}`}>
        {label && <Text className="mb-1 text-sm font-medium">{label}</Text>}

        <div className="relative">
          <Pressable
            {...(Platform.OS === "web"
              ? { onClick: () => !disabled && setOpen(!open) }
              : { onPress: () => !disabled && setOpen(!open) })}
            className={`flex-row items-center border rounded-lg px-3 py-2.5 h-11 ${
              error
                ? "border-red-500"
                : disabled
                ? "border-gray-200 bg-gray-100"
                : "border-gray-200"
            }`}
            disabled={disabled}
          >
            <CalendarIcon
              size={20}
              className={`mr-2 ${disabled ? "text-gray-400" : "text-primary"}`}
            />
            <Text className={disabled ? "text-gray-500" : "text-foreground"}>
              {formattedDate}
            </Text>
          </Pressable>

          {/* Utiliser le Modal avec createPortal pour s'assurer qu'il s'affiche au niveau racine */}
          <Modal
            isOpen={open}
            onClose={() => setOpen(false)}
            title="Sélectionner une date"
          >
            <div className="p-2">
              <CustomCalendarShadcn
                mode="single"
                selected={date}
                onSelect={(newDate) => {
                  setDate(newDate);
                  setOpen(false); // Fermer le modal après sélection
                }}
                disabled={(date: Date) =>
                  (minDate ? isBefore(date, minDate) : false) ||
                  (maxDate ? isAfter(date, maxDate) : false)
                }
                initialFocus
              />
            </div>
          </Modal>
        </div>

        {error && <Text className="mt-1 text-sm text-red-500">{error}</Text>}
      </View>
    );
  }

  // Pour les plateformes natives, nous utilisons le DatePicker existant
  // car react-day-picker ne fonctionne pas sur mobile
  return (
    <View className={`${className}`}>
      {label && <Text className="mb-1 text-sm font-medium">{label}</Text>}

      <Pressable
        {...(Platform.OS === "web"
          ? { onClick: () => !disabled && setOpen(!open) }
          : { onPress: () => !disabled && setOpen(!open) })}
        className={`flex-row items-center border rounded-lg px-3 py-2.5 h-11 ${
          error
            ? "border-red-500"
            : disabled
            ? "border-gray-200 bg-gray-100"
            : "border-gray-200"
        }`}
        disabled={disabled}
      >
        <CalendarIcon
          size={20}
          className={`mr-2 ${disabled ? "text-gray-400" : "text-primary"}`}
        />
        <Text className={disabled ? "text-gray-500" : "text-foreground"}>
          {formattedDate}
        </Text>
      </Pressable>

      {error && <Text className="mt-1 text-sm text-red-500">{error}</Text>}
    </View>
  );
}
