// Script pour diagnostiquer et corriger les problèmes de base de données
import { supabaseAdmin } from './supabase';

export async function diagnoseDatabase() {
  console.log('🔍 Diagnostic de la base de données...');
  
  // Test 1: Vérifier la connexion admin
  try {
    const { data, error } = await supabaseAdmin.from('profiles').select('count').limit(1);
    if (error) {
      console.error('❌ Connexion admin échouée:', error.message);
      return false;
    }
    console.log('✅ Connexion admin OK');
  } catch (e) {
    console.error('❌ Exception lors du test de connexion admin:', e);
    return false;
  }

  // Test 2: Vérifier les tables essentielles
  const tables = ['profiles', 'events', 'participants', 'items'];
  for (const table of tables) {
    try {
      const { error } = await supabaseAdmin.from(table).select('*').limit(1);
      if (error) {
        console.error(`❌ Table ${table} inaccessible:`, error.message);
      } else {
        console.log(`✅ Table ${table} accessible`);
      }
    } catch (e) {
      console.error(`❌ Exception pour la table ${table}:`, e);
    }
  }

  return true;
}

export async function createProfileForUser(userId: string, name: string = 'Utilisateur') {
  console.log(`🔧 Création forcée du profil pour ${userId}...`);
  
  try {
    // Supprimer le profil existant s'il y en a un
    await supabaseAdmin.from('profiles').delete().eq('id', userId);
    
    // Créer un nouveau profil
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: userId,
        name: name,
        avatar_url: null,
        is_discoverable: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Erreur lors de la création forcée du profil:', error.message);
      return null;
    }

    console.log('✅ Profil créé avec succès:', data);
    return data;
  } catch (e) {
    console.error('❌ Exception lors de la création forcée du profil:', e);
    return null;
  }
}

export async function testBasicOperations(userId: string) {
  console.log('🧪 Test des opérations de base...');
  
  // Test 1: Lecture de profil
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle();
    
    if (error) {
      console.error('❌ Lecture de profil échouée:', error.message);
    } else {
      console.log('✅ Lecture de profil OK:', data ? 'Profil trouvé' : 'Aucun profil');
    }
  } catch (e) {
    console.error('❌ Exception lors de la lecture de profil:', e);
  }

  // Test 2: Lecture d'événements
  try {
    const { data, error } = await supabaseAdmin
      .from('events')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('❌ Lecture d\'événements échouée:', error.message);
    } else {
      console.log('✅ Lecture d\'événements OK:', data?.length || 0, 'événements trouvés');
    }
  } catch (e) {
    console.error('❌ Exception lors de la lecture d\'événements:', e);
  }

  // Test 3: Lecture de participants
  try {
    const { data, error } = await supabaseAdmin
      .from('participants')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('❌ Lecture de participants échouée:', error.message);
    } else {
      console.log('✅ Lecture de participants OK:', data?.length || 0, 'participants trouvés');
    }
  } catch (e) {
    console.error('❌ Exception lors de la lecture de participants:', e);
  }

  console.log('🎉 Tests terminés');
}

// Fonction utilitaire pour exécuter tous les diagnostics
export async function runFullDiagnostic(userId: string) {
  console.log('🚀 Démarrage du diagnostic complet...');
  
  await diagnoseDatabase();
  await testBasicOperations(userId);
  
  // Essayer de créer un profil si nécessaire
  const { data: existingProfile } = await supabaseAdmin
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .maybeSingle();
  
  if (!existingProfile) {
    console.log('Aucun profil trouvé, création automatique...');
    await createProfileForUser(userId);
  }
  
  console.log('✅ Diagnostic complet terminé');
}
